<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JKC Jeanswear - Premium Textile Portfolio</title>
    <link href="/src/style.css" rel="stylesheet">
  </head>
  <body class="bg-gray-50 text-gray-900">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm shadow-sm z-50">
      <nav class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="text-2xl font-bold text-indigo-600">JKC Jeanswear</div>
          <div class="hidden md:flex space-x-8">
            <a href="#home" class="text-gray-700 hover:text-indigo-600 transition-colors">Home</a>
            <a href="#about" class="text-gray-700 hover:text-indigo-600 transition-colors">About</a>
            <a href="#portfolio" class="text-gray-700 hover:text-indigo-600 transition-colors">Portfolio</a>
            <a href="#contact" class="text-gray-700 hover:text-indigo-600 transition-colors">Contact</a>
          </div>
          <button class="md:hidden text-gray-700" id="mobile-menu-btn">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
        <!-- Mobile Menu -->
        <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
          <a href="#home" class="block py-2 text-gray-700 hover:text-indigo-600 transition-colors">Home</a>
          <a href="#about" class="block py-2 text-gray-700 hover:text-indigo-600 transition-colors">About</a>
          <a href="#portfolio" class="block py-2 text-gray-700 hover:text-indigo-600 transition-colors">Portfolio</a>
          <a href="#contact" class="block py-2 text-gray-700 hover:text-indigo-600 transition-colors">Contact</a>
        </div>
      </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800 text-white relative overflow-hidden">
      <!-- Hero Background Image -->
      <div class="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center"></div>
      <div class="absolute inset-0 bg-black/50"></div>
      <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml;utf8,<svg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><g fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;><g fill=&quot;%23ffffff&quot; fill-opacity=&quot;0.05&quot;><circle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;2&quot;/></g></g></svg>')"></div>
      <div class="container mx-auto px-4 text-center relative z-10">
        <h1 class="text-5xl md:text-7xl font-bold mb-6 animate-fade-in-up">
          Premium <span class="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-yellow-400">Textile</span> Craftsmanship
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90 animate-fade-in-up animation-delay-200">
          Discover our exquisite collection of premium jeanswear and textile garments, crafted with precision and passion for over two decades.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up animation-delay-400">
          <a href="#portfolio" class="bg-white text-gray-900 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-all transform hover:scale-105 shadow-lg">
            View Portfolio
          </a>
          <a href="#contact" class="border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-gray-900 transition-all transform hover:scale-105">
            Get In Touch
          </a>
        </div>
      </div>
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-6 text-gray-900">About JKC Jeanswear</h2>
          <p class="text-xl text-gray-600 leading-relaxed">
            With over 20 years of experience in the textile industry, JKC Jeanswear has established itself as a premier manufacturer of high-quality denim and casual wear. Our commitment to excellence, innovation, and sustainability drives everything we do.
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8 mb-16">
          <div class="text-center p-6 rounded-lg bg-gray-50 hover:bg-indigo-50 transition-colors">
            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Quality Assurance</h3>
            <p class="text-gray-600">Every garment undergoes rigorous quality checks to ensure premium standards.</p>
          </div>

          <div class="text-center p-6 rounded-lg bg-gray-50 hover:bg-indigo-50 transition-colors">
            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Fast Production</h3>
            <p class="text-gray-600">Efficient manufacturing processes ensure quick turnaround times.</p>
          </div>

          <div class="text-center p-6 rounded-lg bg-gray-50 hover:bg-indigo-50 transition-colors">
            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Sustainable Practices</h3>
            <p class="text-gray-600">Committed to eco-friendly manufacturing and sustainable materials.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-6 text-gray-900">Our Portfolio</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our diverse range of premium textile products, from classic denim to contemporary casual wear.
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Portfolio Item 1 -->
          <div class="group bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="relative overflow-hidden">
              <div class="w-full h-64 bg-gradient-to-br from-blue-400 to-indigo-600 flex items-center justify-center">
                <div class="text-white text-center">
                  <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path>
                  </svg>
                  <p class="text-sm">Premium Denim Jeans</p>
                </div>
              </div>
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Classic Straight Fit</h3>
              <p class="text-gray-600 mb-4">Premium quality denim with perfect fit and comfort. Made from 100% cotton with reinforced stitching.</p>
              <div class="flex justify-between items-center">
                <span class="text-indigo-600 font-semibold">Denim Collection</span>
                <button class="text-indigo-600 hover:text-indigo-800 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Portfolio Item 2 -->
          <div class="group bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="relative overflow-hidden">
              <div class="w-full h-64 bg-gradient-to-br from-purple-400 to-pink-600 flex items-center justify-center">
                <div class="text-white text-center">
                  <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path>
                  </svg>
                  <p class="text-sm">Skinny Fit Jeans</p>
                </div>
              </div>
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Modern Skinny Fit</h3>
              <p class="text-gray-600 mb-4">Contemporary design with stretch fabric for ultimate comfort and style. Perfect for modern lifestyle.</p>
              <div class="flex justify-between items-center">
                <span class="text-indigo-600 font-semibold">Denim Collection</span>
                <button class="text-indigo-600 hover:text-indigo-800 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Portfolio Item 3 -->
          <div class="group bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="relative overflow-hidden">
              <div class="w-full h-64 bg-gradient-to-br from-green-400 to-teal-600 flex items-center justify-center">
                <div class="text-white text-center">
                  <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path>
                  </svg>
                  <p class="text-sm">Casual Chinos</p>
                </div>
              </div>
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Premium Chinos</h3>
              <p class="text-gray-600 mb-4">Versatile casual pants perfect for both work and leisure. Available in multiple colors and fits.</p>
              <div class="flex justify-between items-center">
                <span class="text-indigo-600 font-semibold">Casual Wear</span>
                <button class="text-indigo-600 hover:text-indigo-800 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <!-- Portfolio Item 4 -->
          <div class="group bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="relative overflow-hidden">
              <div class="w-full h-64 bg-gradient-to-br from-orange-400 to-red-600 flex items-center justify-center">
                <div class="text-white text-center">
                  <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path>
                  </svg>
                  <p class="text-sm">Denim Jackets</p>
                </div>
              </div>
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Classic Denim Jacket</h3>
              <p class="text-gray-600 mb-4">Timeless denim jacket with vintage appeal. Perfect layering piece for any season.</p>
              <div class="flex justify-between items-center">
                <span class="text-indigo-600 font-semibold">Outerwear</span>
                <button class="text-indigo-600 hover:text-indigo-800 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Portfolio Item 5 -->
          <div class="group bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="relative overflow-hidden">
              <div class="w-full h-64 bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center">
                <div class="text-white text-center">
                  <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path>
                  </svg>
                  <p class="text-sm">Cargo Pants</p>
                </div>
              </div>
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Utility Cargo Pants</h3>
              <p class="text-gray-600 mb-4">Functional and stylish cargo pants with multiple pockets. Perfect for outdoor activities.</p>
              <div class="flex justify-between items-center">
                <span class="text-indigo-600 font-semibold">Utility Wear</span>
                <button class="text-indigo-600 hover:text-indigo-800 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Portfolio Item 6 -->
          <div class="group bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
            <div class="relative overflow-hidden">
              <div class="w-full h-64 bg-gradient-to-br from-cyan-400 to-blue-600 flex items-center justify-center">
                <div class="text-white text-center">
                  <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"></path>
                  </svg>
                  <p class="text-sm">Denim Shorts</p>
                </div>
              </div>
              <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold mb-2">Summer Denim Shorts</h3>
              <p class="text-gray-600 mb-4">Comfortable and stylish denim shorts perfect for summer. Available in various washes and lengths.</p>
              <div class="flex justify-between items-center">
                <span class="text-indigo-600 font-semibold">Summer Collection</span>
                <button class="text-indigo-600 hover:text-indigo-800 transition-colors">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-6 text-gray-900">Contact Us</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Get in touch with us for all your textile and garment manufacturing needs.
          </p>
        </div>

        <div class="max-w-4xl mx-auto">
          <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
              <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2">Address</h3>
              <p class="text-gray-600">123 Textile Street<br>Manufacturing District<br>City, State 12345</p>
            </div>

            <div class="text-center">
              <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2">Phone</h3>
              <p class="text-gray-600">+****************</p>
            </div>

            <div class="text-center">
              <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2">Email</h3>
              <p class="text-gray-600"><EMAIL></p>
            </div>

            <div class="text-center">
              <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2">Business Hours</h3>
              <p class="text-gray-600">Mon-Fri: 9AM-6PM<br>Sat: 10AM-4PM</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="container mx-auto px-4">
        <div class="grid md:grid-cols-4 gap-8 mb-8">
          <div>
            <div class="text-2xl font-bold text-indigo-400 mb-4">JKC Jeanswear</div>
            <p class="text-gray-400 mb-4">Premium textile manufacturing with over 20 years of experience in creating quality garments.</p>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clip-rule="evenodd"></path>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li><a href="#home" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
              <li><a href="#about" class="text-gray-400 hover:text-white transition-colors">About</a></li>
              <li><a href="#portfolio" class="text-gray-400 hover:text-white transition-colors">Portfolio</a></li>
              <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Services</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Custom Manufacturing</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Quality Control</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Design Consultation</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Bulk Orders</a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
            <ul class="space-y-2 text-gray-400">
              <li>123 Textile Street</li>
              <li>Manufacturing District</li>
              <li>City, State 12345</li>
              <li>+****************</li>
              <li><EMAIL></li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 pt-8 text-center text-gray-400">
          <p>&copy; 2024 JKC Jeanswear. All rights reserved. | Privacy Policy | Terms of Service</p>
        </div>
      </div>
    </footer>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
